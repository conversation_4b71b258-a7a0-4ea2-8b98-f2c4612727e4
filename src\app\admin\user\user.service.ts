import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";

export interface User {
    id?: number;
    username: string;
    email: string;
    phoneNumber?: string;
    password?: string;
    isActive: boolean;
    twoFactorEnabled: boolean;
    avatar?: string;
    avatarFile?: File | null;
    createdAt?: string;
}

@Injectable({
    providedIn: 'root'
})
export class UserService {
    private apiUrl = 'https://localhost:7163/api/Users';

    constructor(private http: HttpClient) { }

    getAll(): Observable<User[]> {
        return this.http.get<User[]>(this.apiUrl);
    }

    getById(id: number): Observable<User> {
        return this.http.get<User>(`${this.apiUrl}/${id}`);
    }

    create(user: FormData): Observable<User> {
        return this.http.post<User>(this.apiUrl, user);
    }

    update(id: number, user: FormData): Observable<void> {
        return this.http.put<void>(`${this.apiUrl}/${id}`, user);
    }

    delete(id: number): Observable<void> {
        return this.http.delete<void>(`${this.apiUrl}/${id}`);
    }


}