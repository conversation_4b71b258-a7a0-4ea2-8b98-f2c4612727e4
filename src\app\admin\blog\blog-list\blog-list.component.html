<div class="blog-list-container">
    <h2><PERSON><PERSON> Blog</h2>
    <button class="btn btn-primary" (click)="addNew()">+z AddPost</button>
    <button class="btn btn-danger" (click)="deleteSelected()" style="margin-left: 10px;">🗑️ Xóa đã chọn</button>

    <div class="filter-date" style="margin: 15px 0;">
        <label for="fromDate">Từ ngày:</label>
        <input id="fromDate" type="date" [(ngModel)]="fromDate" />
        <label for="toDate" style="margin-left: 10px;">Đến ngày:</label>
        <input id="toDate" type="date" [(ngModel)]="toDate" />
        <button (click)="searchByDate()" style="margin-left: 10px;">🔍 Tìm kiếm</button>
    </div>

    <table class="table table-bordered mt-3">
        <thead>
            <tr>
                <th>
                    <input type="checkbox" (change)="toggleSelectAll($event)" />
                </th>
                <th>#</th>
                <th>Ti<PERSON><PERSON></th>
                <th>N<PERSON><PERSON>ng</th>
                <th>Hình Ảnh</th>
                <th>Ngày Tạo</th>
                <th>Hành Động</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let blog of blogs">
                <td>
                    <input type="checkbox" [checked]="selectedBlogIds.includes(blog.postId)" (change)="toggleSelection(blog.postId, $event)" />
                </td>
                <td>{{ blog.postId}}</td>
                <td>{{ blog.title}}</td>
                <td>{{ blog.content}}</td>
                <td>
                    <img [src]="getImage(blog.image)" alt="image" width="100" height="60" *ngIf="blog.image">
                </td>
                <td>{{ blog.createdAt | date:'dd/MM/yyyy HH:mm:ss':'GMT+7'}}</td>
                <td>
                    <button class="btn btn-sm btn-warning" (click)="editBlog(blog.postId)">Sửa</button>
                    <button class="btn btn-sm btn-danger" (click)="deleteBlog(blog.postId)">Xóa</button>
                </td>
            </tr>
        </tbody>
    </table>
</div>