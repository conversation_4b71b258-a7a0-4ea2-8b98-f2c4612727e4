import { Component, OnInit } from '@angular/core';
import { UserService, User } from '../user.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent implements OnInit {
  users: any[] = [];
  selectedUserIds: number[] = [];
  activeFilter: string = '';

  constructor(private userService: UserService, private router: Router) { }

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers(params?: any): void {
    this.userService.getAll().subscribe(data => {
      this.users = data.sort((a, b) => a.id! - b.id!);
      this.applyFilter();
    });
  }

  filterByStatus(): void {
    this.applyFilter();
  }

  applyFilter(): void {
    if (this.activeFilter === '') {
      // Hiển thị tất cả
      this.users = this.users.slice();
    } else {
      const isActive = this.activeFilter === 'true';
      this.users = this.users.filter(u => u.isActive === isActive);
    }
    this.selectedUserIds = [];
  }

  toggleSelection(id: number, event: any): void {
    if (event.target.checked) {
      if (!this.selectedUserIds.includes(id)) {
        this.selectedUserIds.push(id);
      }
    } else {
      this.selectedUserIds = this.selectedUserIds.filter(i => i !== id);
    }
  }

  toggleSelectAll(event: any): void {
    if (event.target.checked) {
      this.selectedUserIds = this.users.map(u => u.id!);
    } else {
      this.selectedUserIds = [];
    }
  }

  getAvatar(avatar: string): string {
    return `https://localhost:7163${avatar}`;
  }

  deleteUser(id: number): void {
    if (confirm('Bạn có chắc chắn muốn xóa không?')) {
      this.userService.delete(id).subscribe(() => {
        this.loadUsers();
      });
    }
  }

  editUser(id: number): void {
    this.router.navigate(['/admin/users/edit', id]);
  }

  addNew(): void {
    this.router.navigate(['/admin/users/add']);
  }
}