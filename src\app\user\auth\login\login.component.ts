import { Component } from '@angular/core';
import { AuthService } from '../auth.service';
import { Router, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  type: string = 'password';
  isText: boolean = false;
  eyeIcon: string = 'fa-eye-slash';
  model = { username: '', password: '' };
  otp: string = '';
  step: 'login' | 'otp' = 'login';
  tempUser: any = null;
  error = '';

  constructor(private auth: AuthService, private router: Router) {}

  ngOnInit() {
    if (localStorage.getItem('user')) {
      this.router.navigate(['/home']);
    }
  }

  login() {
    this.auth.login(this.model).subscribe({
      next: (res: any) => {
        if (res.requireOtp) {
          this.step = 'otp';
          this.tempUser = res.tempUser;
        } else {
          localStorage.setItem('user', JSON.stringify(res));
          this.router.navigate([res.role === 'admin' ? '/admin' : '/home']);
        }
      },
      error: err => this.error = err.error || 'Đăng nhập thất bại'
    });
  }

  verifyOtp() {
    this.auth.verifyOtp({ ...this.tempUser, otp: this.otp }).subscribe({
      next: (res: any) => {
        localStorage.setItem('user', JSON.stringify(res));
        this.router.navigate([res.role === 'admin' ? '/admin' : '/home']);
      },
      error: err => this.error = err.error || 'Xác nhận OTP thất bại'
    });
  }

  showPassword() {
    this.isText = !this.isText;
    this.eyeIcon = this.isText ? 'fa-eye' : 'fa-eye-slash';
    this.type = this.isText ? 'text' : 'password';
  }
}
