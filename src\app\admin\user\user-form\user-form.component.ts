import { Component, OnInit } from '@angular/core';
import { User, UserService } from '../user.service';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-user-form',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './user-form.component.html',
  styleUrls: ['./user-form.component.scss']
})
export class UserFormComponent implements OnInit {
  isEdit = false;
  isLoading = false;

  model: User = {
    id: 0,
    username: '',
    email: '',
    phoneNumber: '',
    isActive: true,
    twoFactorEnabled: false,
    avatar: '',
    password: ''
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService
  ) { }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEdit = true;
      this.loadUser(+id);
    }
  }

  loadUser(id: number): void {
    this.isLoading = true;
    this.userService.getById(id).subscribe({
      next: (data) => {
        this.model = { ...data, password: '' }; // Không hiển thị password cũ
        this.isLoading = false;
      },
      error: () => {
        alert('Không thể tải dữ liệu');
        this.router.navigate(['/admin/users']);
        this.isLoading = false;
      }
    });
  }

  getAvatar(avatar?: string): string {
    return avatar ? (avatar.startsWith('http') ? avatar : `https://localhost:7163${avatar}`) : '';
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.model.avatarFile = input.files[0];
    }
  }

  save(): void {
    this.isLoading = true;
    const formData = new FormData();
    formData.append('username', this.model.username);
    formData.append('email', this.model.email);
    formData.append('phoneNumber', this.model.phoneNumber ?? '');
    if (this.model.password) {
      formData.append('password', this.model.password);
    }
    formData.append('isActive', String(this.model.isActive));
    formData.append('twoFactorEnabled', String(this.model.twoFactorEnabled));
    
    if (this.model.avatarFile) {
      formData.append('avatarFile', this.model.avatarFile);
    }

    if (this.isEdit && this.model.id) {
      this.userService.update(this.model.id, formData).subscribe({
        next: () => {
          this.isLoading = false;
          this.router.navigate(['/admin/users']);
        },
        error: () => {
          this.isLoading = false;
          alert('Cập nhật thất bại');
        }
      });
    } else {
      this.userService.create(formData).subscribe({
        next: () => {
          this.isLoading = false;
          this.router.navigate(['/admin/users']);
        },
        error: () => {
          this.isLoading = false;
          alert('Thêm mới thất bại');
        }
      });
    }
  }
}