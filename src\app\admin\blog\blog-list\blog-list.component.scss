.blog-list-container {
  padding: 20px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;

  h2 {
    margin-bottom: 20px;
    font-weight: 700;
    font-size: 1.8rem;
    color: #222;
    user-select: none;
  }

  button.btn-primary {
    background-color: #007bff;
    border: none;
    padding: 10px 18px;
    font-weight: 600;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #0056b3;
    }
  }

  table.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    background-color: #fefefe;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    thead {
      background-color: #f3f6f9;

      th {
        padding: 14px 12px;
        text-align: left;
        font-weight: 600;
        font-size: 1rem;
        color: #555;
        border-bottom: 2px solid #e1e7ee;
        user-select: none;
      }
    }

    tbody {
      tr {
        transition: background-color 0.2s ease;

        &:nth-child(even) {
          background-color: #fafafa;
        }

        &:hover {
          background-color: #e9f0fb;
        }

        td {
          padding: 12px 10px;
          vertical-align: middle;
          font-size: 0.9rem;
          color: #444;

          img {
            border-radius: 5px;
            object-fit: cover;
            max-width: 100px;
            max-height: 60px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
          }

          button {
            margin-right: 8px;
            padding: 5px 10px;
            font-size: 0.85rem;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: background-color 0.25s ease;

            &.btn-warning {
              background-color: #ffc107;
              color: #212529;

              &:hover {
                background-color: #e0a800;
              }
            }

            &.btn-danger {
              background-color: #dc3545;
              color: white;

              &:hover {
                background-color: #bb2d3b;
              }
            }

            &.btn-sm {
              padding: 4px 8px;
            }
          }
        }
      }
    }
  }
}
