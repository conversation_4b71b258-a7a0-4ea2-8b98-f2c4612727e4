<div class="user-form-container">
  <span *ngIf="isLoading" class="loader"></span>

  <h2>{{ isEdit ? 'Sửa' : 'Thêm mới' }} User</h2>

  <form #f="ngForm" (ngSubmit)="save()" novalidate>
    <div class="form-group">
      <label>Username:</label>
      <input class="form-control" [(ngModel)]="model.username" name="username" required />
    </div>

    <div class="form-group">
      <label>Email:</label>
      <input class="form-control" [(ngModel)]="model.email" name="email" required />
    </div>

    <div class="form-group">
      <label>Phone:</label>
      <input class="form-control" [(ngModel)]="model.phoneNumber" name="phoneNumber" />
    </div>

    <div class="form-group">
      <label>Password:</label>
      <input type="password" class="form-control" [(ngModel)]="model.password" name="password" [required]="!isEdit" />
      <small *ngIf="isEdit">(<PERSON><PERSON> trống nếu không đổi mật khẩu)</small>
    </div>

    <div class="form-group">
      <label>Active:</label>
      <input type="checkbox" [(ngModel)]="model.isActive" name="isActive" />
    </div>

    <div class="form-group">
      <label>Two Factor:</label>
      <input type="checkbox" [(ngModel)]="model.twoFactorEnabled" name="twoFactorEnabled" />
    </div>

    <div class="form-group">
      <label>Avatar:</label>
      <input type="file" (change)="onFileSelected($event)" name="avatarFile" />
      <div *ngIf="model.avatar" class="image-preview mt-2">
        <img [src]="getAvatar(model.avatar)" alt="avatar" width="80" height="80" style="object-fit:cover;border-radius:50%">
      </div>
    </div>

    

    <div class="form-actions mt-3">
      <button type="submit" class="btn btn-primary" [disabled]="f.invalid">Lưu</button>
      <button type="button" class="btn btn-secondary ml-2" routerLink="/admin/users">Hủy</button>
    </div>
  </form>
</div>