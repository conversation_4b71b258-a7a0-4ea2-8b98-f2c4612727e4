import { Component, OnInit } from '@angular/core';
import { BlogPost, BlogService } from '../blog.service';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-blog-form',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './blog-form.component.html',
  styleUrls: ['./blog-form.component.scss']
})
export class BlogFormComponent implements OnInit {
  isEdit = false;
  isLoading = false;

  model: BlogPost = {
    postId: 0,
    title: '',
    image: '',
    content: '',
    createdAt: new Date().toISOString()
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private blogService: BlogService
  ) { }
   // kiểm tra nếu URL có id thì sẽ chuyển sang chế độ chỉnh sửa và tải dữ liệu blog cần sửa. 
   // Nếu không có id thì là chế độ tạo mới.
  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id'); // Lấy giá trị tham số id từ URL
    if (id) {
      this.isEdit = true; 
      this.loadBlog(+id); // để lấy dữ liệu blog từ server (dấu + chuyển id từ string sang number).
    }
  }
  loadBlog(id: number): void {
    this.isLoading = true; // Bật trạng thái loading
    this.blogService.getById(id).subscribe({  // Lấy dữ liệu từ API
      next: (data) => { // Gán dữ liệu vào model
        this.model = data; // Gán dữ liệu vào model
        this.isLoading = false; // Tắt trạng thái loading
      },
      error: () => { // Xử lý lỗi
        alert('Không thể tải dữ liệu'); // Thông báo lỗi
        this.router.navigate(['/admin/blogs']); // Chuyển hướng về trang danh sách blog
        this.isLoading = false; // Tắt trạng thái loading
      }
    });
  }

  getImage(image?: string): string {
    return `https://localhost:7163${image}`; 
  }

  onFileSelected(event: Event) { // Sự kiện kích hoạt khi người dùng truyền qua file
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {  // Kiểm tra xem có file được chọn không
      this.model.file = input.files[0]; // Lấy file đầu tiên trong danh sách file được chọn
    }
  }

  save(): void {
    this.isLoading = true;
    this.isLoading = true;

    const formData = new FormData();
    formData.append('title', this.model.title);
    formData.append('content', this.model.content);

    if (this.model.file) {
      formData.append('file', this.model.file); // Đây là File thực sự
    }
    if (this.isEdit) {

      this.blogService.update(this.model.postId, formData).subscribe({
        next: () => {
          this.isLoading = false;
          this.router.navigate(['/admin/blogs']);
        },
        error: () => {
          this.isLoading = false;
          alert('Cập nhật thất bại');
        }
      });
    } else {
      this.blogService.create(formData).subscribe({
        next: () => {
          this.isLoading = false;
          this.router.navigate(['/admin/blogs']);
        },
        error: () => {
          this.isLoading = false;
          alert('Thêm mới thất bại');
        }
      });
    }
  }

}
