import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";

@Injectable({ providedIn: 'root' })
export class AuthService {
    private apiUrl = 'https://localhost:7163/api/Auth';

    constructor(private http: HttpClient) { }

    register(data: { username: string; email: string; password: string }): Observable<any> {
        return this.http.post(`${this.apiUrl}/register`, data);
    }

    login(data: { username: string; password: string }): Observable<any> {
        return this.http.post(`${this.apiUrl}/login`, data);
    }
    verifyOtp(data: any): Observable<any> {
        return this.http.post(`${this.apiUrl}/verify-otp`, data);
    }
}