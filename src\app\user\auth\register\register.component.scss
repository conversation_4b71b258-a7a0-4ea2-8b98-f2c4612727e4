.outer{
    height: 100vh;
    background: linear-gradient(to top, #c9c9ff 50%, #6e6ef6 90%) no-repeat;
}

.panel-heading{
    text-align: center;
    margin-bottom: 10px;
}

#forget{
    min-width: 100px;
    margin-left: auto;
    text-decoration: none;
    cursor: pointer;
}

a:hover{
    text-decoration: none;
}

.form-inline label{
    padding-left: 10px;
    margin: 0;
    cursor: pointer;
}

.btn{
    margin-top: 20px;
    border-radius: 15px;
}

.panel{
    min-height: 380px;
    box-shadow: 20px 20px 80px rgba(218, 218, 218);
    border-radius: 12px;
    margin-top: 100px;
}
.input-field{
    border-radius: 5px;
    padding: 5px;
    display: flex;
    align-items: center;
    cursor: pointer;
    border: 1px solid #ddd;
    color: #4343ff;
}

.input-field.error{
    border: 1px solid red;
    color: red;
}

input[type="text"],
input[type="password"]{
    border: none;
    outline: none;
    box-shadow: none;
    width: 100%;
}
.fa-eye-slash.btn{
    border: none;
    outline: none;
    box-shadow: none;
}
img{
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
    position: relative;
}
