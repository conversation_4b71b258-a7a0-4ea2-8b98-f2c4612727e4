import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

export interface BlogPost {
  postId: number;
  title: string;
  content: string;
  image?: string;
  file?: File | null;
  createdAt: string;
}

@Injectable({
  providedIn: 'root'
})
export class BlogService {
  private apiUrl = 'https://localhost:7163/api/BlogPosts';
  constructor(private http: HttpClient) { }


  getAll(params?: any) {
    let query = ''; 
    if (params) {
      const queryParams = []; // Tạo một mảng queryParams chứa các chuỗi dạng key=value
      if (params.fromDate) queryParams.push(`fromDate=${params.fromDate}`); // thêm "fromDate=giá trị" vào mảng.
      if (params.toDate) queryParams.push(`toDate=${params.toDate}`); //  thêm "toDate=giá trị" vào mảng.
      if (queryParams.length) query = '?' + queryParams.join('&'); // chuỗi truy vấn query string
    }
    return this.http.get<any[]>(`https://localhost:7163/api/BlogPosts${query}`);
  }
  getById(id: number): Observable<BlogPost> {
    return this.http.get<BlogPost>(`${this.apiUrl}/${id}`);
  }
  create(post: FormData): Observable<BlogPost> {
    return this.http.post<BlogPost>(this.apiUrl, post);
  }
  update(id: number, post: FormData): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, post);
  }
  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
  deleteMultiple(ids: number[]) {
    return this.http.post('https://localhost:7163/api/BlogPosts/delete-multipath', ids);
  }
}
