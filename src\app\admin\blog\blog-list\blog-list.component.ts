import { Component, OnInit } from '@angular/core';
import { BlogPost, BlogService } from '../blog.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-blog-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './blog-list.component.html',
  styleUrls: ['./blog-list.component.scss']

})
export class BlogListComponent implements OnInit {
  blogs: any[] = [];
  selectedBlogIds: number[] = [];
  fromDate: string = '';
  toDate: string = '';

  constructor(private blogService: BlogService, private router: Router) { }

  ngOnInit(): void {
    this.loadBlogs();
  }

  loadBlogs(params?: any): void {
    this.blogService.getAll(params).subscribe(data => {
      this.blogs = data.sort((a, b) => a.postId - b.postId);
    });
  }

  searchByDate(): void {
    if (!this.fromDate && !this.toDate) {
      this.loadBlogs();
      return;
    }
    const params: any = {};
    if (this.fromDate) params.fromDate = this.fromDate;
    if (this.toDate) params.toDate = this.toDate;
    this.loadBlogs(params);
  }

  toggleSelection(id: number, event: any): void {
    if (event.target.checked) {
      if (!this.selectedBlogIds.includes(id)) {
        this.selectedBlogIds.push(id);
      }
    } else {
      this.selectedBlogIds = this.selectedBlogIds.filter(i => i !== id);
    }
  }

  toggleSelectAll(event: any): void {
    if (event.target.checked) {
      this.selectedBlogIds = this.blogs.map(b => b.postId);
    } else {
      this.selectedBlogIds = [];
    }
  }

  deleteSelected(): void {
    if (this.selectedBlogIds.length === 0) {
      alert('Vui lòng chọn ít nhất một blog để xóa.');
      return;
    }
    if (!confirm('Bạn có chắc chắn muốn xóa các blog đã chọn không?')) return;
    this.blogService.deleteMultiple(this.selectedBlogIds).subscribe(() => {
      this.loadBlogs(); // Load lại danh sách từ server
      this.selectedBlogIds = [];
    }, err => {
      alert('Lỗi khi xóa nhiều blog');
    });
  }

  getImage(image: string): string {
    return `https://localhost:7163${image}`;
  }

  deleteBlog(id: number): void {
    if (confirm('Bạn có chắc chắn muốn xóa không')) {
      this.blogService.delete(id).subscribe(() => {
        this.loadBlogs();
      });
    }
  }
  editBlog(id: number): void {
    this.router.navigate(['/admin/blogs/edit', id]);
  }
  addNew(): void {
    this.router.navigate(['/admin/blogs/add']);
  }
}
