import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { CategoryDto, Ebook } from '../../models/ebook.model';

@Component({
  selector: 'app-form',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, HttpClientModule],
  templateUrl: './form.component.html',
  styleUrls: ['./form.component.scss']
})
export class FormComponent {

  categories: CategoryDto[] = [];

  isEdit = false;
  isLoading = false;

  model: Ebook = {
    ebookId: 0,
    title: '',
    author: '',
    description: '',
    fileUrl: '',
    fileType: 'PDF',
    price: 0,
    categoryId: 1,
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private http: HttpClient
  ) {
    this.getCategories(); 
    const id = this.route.snapshot.paramMap.get('id'); // Lấy id từ URL
    if (id) { // Kiểm tra xem có id không
      this.isEdit = true; 
      this.loadEbook(+id); // Lấy ebook từ API
    }
  }

  getCategories() {
    this.http.get<CategoryDto[]>('https://localhost:7163/api/category').subscribe({
      next: (data) => { 
        this.categories = data; // Gán dữ liệu vào model
      },
      error: (err) => { // Xử lý lỗi
        console.error('Lỗi khi lấy danh sách ebooks:', err);
      }
    });
  }

  onFileSelected(event: Event) { // Sự kiện kích hoạt khi người dùng truyền qua file
    const input = event.target as HTMLInputElement; // Chuyển đổi event.target thành HTMLInputElement
    if (input.files && input.files.length > 0) { // Kiểm tra xem có file được chọn không
      this.model.file = input.files[0]; // Lấy file đầu tiên trong danh sách file được chọn
    }
  }

  loadEbook(id: number) {
    this.http.get<any>(`https://localhost:7163/api/ebook/${id}`).subscribe({ // Lấy dữ liệu từ API
      next: (data) => { // Gán dữ liệu vào model
        this.model = data; 
      },
      error: () => { // Xử lý lỗi
        alert('Không thể tải dữ liệu'); // Thông báo lỗi
        this.router.navigate(['/admin/ebooks']); 
      }
    });
  }

  save() { // Lưu ebook
    this.isLoading = true; 

    const formData = new FormData(); 
    formData.append('title', this.model.title); 
    formData.append('description', this.model.description);
    formData.append('author', this.model.author);
    formData.append('fileType', this.model.fileType);
    formData.append('price', this.model.price.toString()); 
    formData.append('categoryId', this.model.categoryId.toString());

    if (this.model.file) { // Kiểm tra xem có file được chọn không
      formData.append('file', this.model.file);  // Lấy file đầu tiên trong danh sách file được chọn
    }

    if (this.isEdit) { // Kiểm tra xem có đang sửa không
      this.http.put(`https://localhost:7163/api/ebook/${this.model.ebookId}`, formData).subscribe({ 
        next: () => { // Sau khi cập nhật thành công
          this.isLoading = false; // Tắt trạng thái loading
          this.router.navigate(['/admin/ebooks']); // Chuyển hướng về trang danh sách ebook
        },
        error: () => { // Xử lý lỗi
          this.isLoading = false; // Tắt trạng thái loading
          alert('Cập nhật thất bại');
        }
      });
    } else {
      this.http.post<any>('https://localhost:7163/api/ebook', formData).subscribe({ 
        next: () => { // Sau khi thêm mới thành công 
          this.isLoading = false; // Tắt trạng thái loading
          this.router.navigate(['/admin/ebooks']); 
        },
        error: () => {
          this.isLoading = false;
          alert('Thêm mới thất bại');
        }
      });
    }
  }


}
