export interface Ebook {
  ebookId: number;
  title: string;
  description: string;
  author: string;
  fileUrl: string;
  fileType: string;
  price: number;
  categoryName?: string;
  createdAt?: string;
  categoryId: number;
  file?: File | null;
  selected?: boolean; // <== thêm dòng này
}
export interface EbookDto {
  title: string;
  description: string;
  author: string;
  file?: File; // dùng cho FormData
  fileType: string;
  price: number;
  categoryId: string;
}
export interface CategoryDto {
  categoryId: number;
  categoryName: string;
}