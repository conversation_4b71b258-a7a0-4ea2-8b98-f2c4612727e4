import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { CategoryDto, Ebook } from '../../models/ebook.model';
import { HttpClientModule, HttpClient, HttpParams } from '@angular/common/http';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-list',
  standalone: true,
  imports: [CommonModule, RouterModule, HttpClientModule, FormsModule,],
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss']
})
export class ListComponent implements OnInit {
  ebooks: Ebook[] = [];
  categories: CategoryDto[] = [];

  fromDate: string = '';
  toDate: string = '';
  selectedEbookIds: number[] = [];


  constructor(private http: HttpClient) {}

  ngOnInit(): void {
    this.getEbooks();
  }

  getEbooks(params?: HttpParams) { 
    this.http.get<Ebook[]>('https://localhost:7163/api/ebook', { params }).subscribe({
      next: (data) => {
        this.ebooks = data;
      },
      error: (err) => {
        console.error('Lỗi khi lấy danh sách ebooks:', err);
      }
    });
  }

  searchByDate() { // Tìm kiếm theo ngày tạo 
    let params = new HttpParams(); // Tạo đối tượng HttpParams

    if (this.fromDate) { // Kiểm tra xem có fromDate không
      params = params.set('fromDate', this.fromDate); // Thêm fromDate vào params
    }
    if (this.toDate) { // Kiểm tra xem có toDate không
      params = params.set('toDate', this.toDate); // Thêm toDate vào params
    }

    this.getEbooks(params); // Gọi lại API với params
  }

  getFileName(fileUrl: string): string { // Lấy tên file từ đường dẫn
    return fileUrl?.split('/').pop() ?? ''; // Lấy phần cuối của đường dẫn
  }

  toggleSelection(id: number, event: any) { // Chọn ebook
    if (event.target.checked) { // Kiểm tra xem có được chọn không
      this.selectedEbookIds.push(id); // Thêm id vào danh sách ebook đã chọn
    } else { // Nếu không được chọn
      this.selectedEbookIds = this.selectedEbookIds.filter(x => x !== id); // Loại bỏ id khỏi danh sách ebook đã chọn
    }
  }
  toggleSelectAll(event: Event) { // Chọn tất cả ebook
  const checked = (event.target as HTMLInputElement).checked; 
  this.ebooks.forEach(e => this.toggleSelection(e.ebookId, { target: { checked } })); // Duyệt qua danh sách ebook và chọn hoặc bỏ chọn ebook
}

  deleteSelected() { // Xóa ebook đã chọn
    if (this.selectedEbookIds.length === 0) { // Kiểm tra xem có ebook nào được chọn không
      alert('Vui lòng chọn ít nhất một ebook để xóa.'); 
      return;
    }

    if (!confirm('Bạn có chắc chắn muốn xóa các ebook đã chọn không?')) return;

    this.http.request('delete', 'https://localhost:7163/api/ebook/delete-multiple', { // Gọi API xóa ebook
      body: this.selectedEbookIds // Truyền danh sách ebook đã chọn vào body
    }).subscribe({ 
      next: () => {
        this.ebooks = this.ebooks.filter(e => !this.selectedEbookIds.includes(e.ebookId)); // Lọc ebook đã chọn khỏi danh sách ebook
        this.selectedEbookIds = []; 
      },
      error: (err) => {
        console.error('Lỗi khi xóa nhiều ebook:', err);
      }
    });
  }

  deleteEbook(id: number) { 
    if (!confirm('Bạn có chắc chắn muốn xóa ebook này không?')) return;
    const arr = [id] 
    this.http.request('delete', 'https://localhost:7163/api/ebook/delete-multiple', { 
      body: arr 
    }).subscribe({
      next: () => {
        this.ebooks = this.ebooks.filter(e => e.ebookId !== id);
      },
      error: (err) => {
        console.error('Lỗi khi xóa ebook:', err);
      }
    });
  }
}
