<div class="outer">
    <div class="container">
        <div class="row">
            <div class="offset-md-2 col-lg-5 col-md-7 offset-lg-4 offset-md-3">
                <div class="panel border bg-white">
                    <div class="panel-heading">
                        <h3 class="pt-3 font-weight-bold">Sign Up</h3>
                    </div>
                    <div class="panel-body p-3">
                        <form (ngSubmit)="register()" #f="ngForm">
                            <div class="form-group pt-3">
                                <div class="input-field">
                                    <span class="fa fa-user p-2"></span>
                                    <input type="text" placeholder="Username"
                                        [(ngModel)]="model.username" name="username" required class="form-control">
                                </div>
                            </div>
                            <div class="form-group pt-3">
                                <div class="input-field">
                                    <span class="fa fa-envelope p-2"></span>
                                    <input type="text" placeholder="Email"
                                        [(ngModel)]="model.email" name="email" required class="form-control">
                                </div>
                            </div>
                            <div class="form-group pt-3">
                                <div class="input-field">
                                    <span class="fa fa-lock p-2"></span>
                                    <input [type]="type" placeholder="Password"
                                        [(ngModel)]="model.password" name="password" required class="form-control">
                                    <span (click)="showPassword()" class="fa" [ngClass]="eyeIcon"></span>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block mt-3 w-100" [disabled]="f.invalid">Sign Up</button>
                            <div *ngIf="error" style="color:red; margin-top:10px;">{{error}}</div>
                        </form>
                        <div class="text-center pt-4 text-muted">
                            Already have an account? <a routerLink="/login">Login</a>
                        </div>
                    </div>
                    <div class="mx-3 my-2 py-2 bordert">
                        <div class="text-center py-3">
                            <!-- Social login nếu muốn -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>