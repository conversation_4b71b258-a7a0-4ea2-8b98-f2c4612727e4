import { Routes } from '@angular/router';
import { AdminLayoutComponent } from './admin/layout/layout.component';
import { DashboardComponent } from './admin/dashboard/dashboard.component';
import { UserLayoutComponent } from './user/layout/layout.component';
import { HomeComponent } from './user/home/<USER>';
import { EbookComponent } from './user/ebook/ebook.component';
import { ListComponent } from './admin/ebooks/list/list.component';
import { FormComponent } from './admin/ebooks/form/form.component';
import { BlogListComponent } from './admin/blog/blog-list/blog-list.component';
import { BlogFormComponent } from './admin/blog/blog-form/blog-form.component';
import { UserListComponent } from './admin/user/user-list/user-list.component';
import { UserFormComponent } from './admin/user/user-form/user-form.component';
import { LoginComponent } from './user/auth/login/login.component';
import { RegisterComponent } from './user/auth/register/register.component';
import { AdminGuard } from './user/auth/admin.guard';
import { AuthGuard } from './user/auth/auth.guard';

export const routes: Routes = [
  {
    path: 'admin',
    component: AdminLayoutComponent,
    canActivate: [AdminGuard, AuthGuard],
    children: [
      {
        path: 'dashboard',
        component: DashboardComponent
      },
      {
        path: 'ebooks',
        children: [
          { path: '', component: ListComponent },
          { path: 'add', component: FormComponent },
          { path: 'edit/:id', component: FormComponent }
        ]
      },
      {
        path: 'blogs',
        children: [
          { path: '', component: BlogListComponent},
          { path: 'add', component: BlogFormComponent },
          { path: 'edit/:id', component: BlogFormComponent }
        ]
      },
      {
        path: 'users',
        children: [
          { path: '', component: UserListComponent},
          { path: 'add', component: UserFormComponent },
          { path: 'edit/:id', component: UserFormComponent }
        ]
      }
      // các route con khác
    ]
  },

  { path: 'login', component: LoginComponent},
  { path: 'register', component: RegisterComponent},
  {
    path: '',
    component: UserLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'home',
        component: HomeComponent
      },
      {
        path: 'ebooks',
        component: EbookComponent
      },
    ]
  }
];
