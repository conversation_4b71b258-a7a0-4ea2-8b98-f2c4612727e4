/* Container */
.container {
  max-width: 1200px;
  margin: 32px auto;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 28px;
  color: #2c3e50;
}

/* Buttons */
.btn {
  padding: 6px 12px;
  font-size: 14px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
}

.btn-add {
  background-color: #2ecc71;
  color: white;
}

.btn-edit {
  background-color: #3498db;
  color: white;
  margin-right: 4px;
}

.btn-delete {
  background-color: #e74c3c;
  color: white;
}

/* Table styles */
.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  text-align: left;
}

.table th,
.table td {
  padding: 12px 10px;
  border-bottom: 1px solid #e0e0e0;
}

.table th {
  background-color: #f8f9fa;
  color: #34495e;
  font-weight: 600;
}

.table tr:hover {
  background-color: #f1f1f1;
}

/* Tag for file type */
.tag {
  display: inline-block;
  padding: 2px 6px;
  background-color: #ecf0f1;
  border-radius: 4px;
  font-size: 12px;
  color: #555;
}
