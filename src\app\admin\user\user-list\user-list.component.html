<div class="user-list-container">
  <h2><PERSON><PERSON></h2>
  <button class="btn btn-primary" (click)="addNew()">+ Thêm User</button>

  <div class="filter-status" style="margin: 15px 0;">
    <label for="activeFilter">Trạng thái:</label>
    <select id="activeFilter" [(ngModel)]="activeFilter" (change)="filterByStatus()">
      <option value="">Tất cả</option>
      <option value="true">Đang hoạt động</option>
      <option value="false"><PERSON><PERSON> khóa</option>
    </select>
  </div>

  <table class="table table-bordered mt-3">
    <thead>
      <tr>
        <th>#</th>
        <th>Avatar</th>
        <th>Username</th>
        <th>Email</th>
        <th>Phone</th>
        <th>Active</th>
        <th>2FA</th>
        <th>Ng<PERSON>y tạo</th>
        <th><PERSON><PERSON><PERSON></th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let user of users">
        <td>{{ user.id }}</td>
        <td>
          <img *ngIf="user.avatar" [src]="getAvatar(user.avatar)" alt="avatar" width="50" height="50" style="object-fit:cover;border-radius:50%">
        </td>
        <td>{{ user.username }}</td>
        <td>{{ user.email }}</td>
        <td>{{ user.phoneNumber }}</td>
        <td>{{ user.isActive ? '✔️' : '❌' }}</td>
        <td>{{ user.twoFactorEnabled ? '✔️' : '❌' }}</td>
        <td>{{ user.createdAt | date:'dd/MM/yyyy HH:mm:ss':'GMT+7' }}</td>
        <td>
          <button class="btn btn-sm btn-warning" (click)="editUser(user.id)">Sửa</button>
          <button class="btn btn-sm btn-danger" (click)="deleteUser(user.id)">Xóa</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>