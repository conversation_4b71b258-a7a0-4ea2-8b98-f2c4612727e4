<span *ngIf="isLoading" class="loader"></span>

<h2>{{ isEdit ? 'Sửa' : 'Thêm mới' }} Ebook</h2>

<form #f="ngForm" (ngSubmit)="save()" novalidate>
  <div>
    <label>Ti<PERSON><PERSON> đề:</label>
    <input [(ngModel)]="model.title" name="title" required />
  </div>

  <div>
    <label>Tác giả:</label>
    <input [(ngModel)]="model.author" name="author" required />
  </div>

  <div>
    <label>Mô tả:</label>
    <textarea [(ngModel)]="model.description" name="description" required></textarea>
  </div>

  <div>
    <label>Loại file:</label>
    <select [(ngModel)]="model.fileType" name="fileType" required>
      <option value="PDF">PDF</option>
      <option value="Word">Word</option>
    </select>
  </div>

  <div>
    <label>Giá:</label>
    <input type="number" [(ngModel)]="model.price" name="price" required min="0" />
  </div>

  <div>
    <label>Link file:</label>
    <input type="file" (change)="onFileSelected($event)" name="file" required />
  </div>

  <div>
  <label>Danh mục:</label>
  <select [(ngModel)]="model.categoryId" name="categoryId" required>
    <option [value]="0" disabled>-- Select category --</option>
    <option *ngFor="let c of categories" [value]="c.categoryId">{{ c.categoryName }}</option>
</select>
</div>

  <button type="submit" [disabled]="f.invalid">Lưu</button>
</form>
