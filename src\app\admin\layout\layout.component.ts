import { Component } from '@angular/core';
import { Router, RouterModule, RouterOutlet } from '@angular/router';
import { DashboardComponent } from '../dashboard/dashboard.component'; // Đường dẫn đúng
import { HeaderComponent } from '../components/header/header.component';
import { CommonModule } from '@angular/common';
// hoặc có thể import tất cả components con dùng trong layout

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [RouterModule, CommonModule],
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss']
})
export class AdminLayoutComponent {
  user: any = {};
  constructor(private router: Router) {}

  ngOnInit() {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      this.user = JSON.parse(userStr);
    }
  }
  getAvatar(avatar?: string): string {
  return avatar
    ? (avatar.startsWith('http') ? avatar : 'https://localhost:7163' + avatar)
    : 'assets/default-avatar.png';
}

  logout() {
    localStorage.removeItem('user');
    this.router.navigate(['/login']);
  }
}
