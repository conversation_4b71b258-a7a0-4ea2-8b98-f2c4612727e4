<div class="container">
  <div class="header">
    <h2>📚 Danh sách Ebook</h2>
    <a class="btn btn-add" routerLink="/admin/ebooks/add">➕ Thêm Ebook</a>
    <button class="btn btn-delete" (click)="deleteSelected()">🗑️ Xóa đã chọn</button>
  </div>

  <div class="filter-date" style="margin-bottom: 20px;">
    <label for="fromDate">Từ ngày:</label>
    <input id="fromDate" type="date" [(ngModel)]="fromDate" />

    <label for="toDate" style="margin-left: 10px;">Đến ngày:</label>
    <input id="toDate" type="date" [(ngModel)]="toDate" />

    <button (click)="searchByDate()" style="margin-left: 10px;">🔍 Tìm kiếm</button>
  </div>
  <table class="table">
    <thead>
      <tr>
        <th>
          <input type="checkbox" (change)="toggleSelectAll($event)" />
        </th>
        <th>#</th>
        <th>Tiêu đề</th>
        <th>Mô tả</th>
        <th>Tác giả</th>
        <th>File</th>
        <th>Giá</th>
        <th>Ngày tạo</th>
        <th>Danh mục</th>
        <th>Hành động</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let ebook of ebooks">
        <td>
          <input type="checkbox" [checked]="selectedEbookIds.includes(ebook.ebookId)" (change)="toggleSelection(ebook.ebookId, $event)" />
        </td>
        <td>{{ ebook.ebookId }}</td>
        <td>{{ ebook.title }}</td>
        <td>{{ ebook.description }}</td>
        <td>{{ ebook.author }}</td>
        <td>
        <span class="tag">{{ ebook.fileType }}</span><br />
        <a [href]="'https://localhost:7163/api/ebook/download/' + getFileName(ebook.fileUrl)" target="_blank">
          📥 Tải về
        </a>
      </td>
        <td>{{ ebook.price | number }} đ</td>
        <td>{{ ebook.createdAt  | date:'dd/MM/yyyy HH:mm:ss':'GMT+7' }}</td>
        <td>{{ ebook.categoryName }}</td>
        <td>
          <a class="btn btn-edit" [routerLink]="['/admin/ebooks/edit', ebook.ebookId]">✏️</a>
           <button class="btn btn-delete" (click)="deleteEbook(ebook.ebookId)">🗑️</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
