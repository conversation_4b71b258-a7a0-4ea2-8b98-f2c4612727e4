import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Ebook, EbookDto } from '../models/ebook.model';

@Injectable({
  providedIn: 'root'
})
export class EbookService {
  private apiUrl = 'https://localhost:7163';

  constructor(private http: HttpClient) {}

  getAll(): Observable<Ebook[]> {
    return this.http.get<Ebook[]>(this.apiUrl);
  }

  getById(id: number): Observable<Ebook> {
    return this.http.get<Ebook>(`${this.apiUrl}/${id}`);
  }

  create(ebookDto: EbookDto): Observable<Ebook> {
    const formData = this.toFormData(ebookDto);
    return this.http.post<Ebook>(this.apiUrl, formData);
  }

  update(id: number, ebookDto: EbookDto): Observable<void> {
    const formData = this.toFormData(ebookDto);
    return this.http.put<void>(`${this.apiUrl}/${id}`, formData);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  private toFormData(ebookDto: EbookDto): FormData {
    const formData = new FormData();
    formData.append('Title', ebookDto.title);
    formData.append('Author', ebookDto.author);
    formData.append('Description', ebookDto.description);
    formData.append('FileType', ebookDto.fileType);
    formData.append('Price', ebookDto.price.toString());
    formData.append('CategoryId', ebookDto.categoryId.toString());

    if (ebookDto.file) {
      formData.append('File', ebookDto.file);
    }

    return formData;
  }
}
