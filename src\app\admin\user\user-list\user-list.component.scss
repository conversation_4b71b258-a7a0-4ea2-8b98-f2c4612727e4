.user-list-container {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 0 10px rgba(0,0,0,0.08);
  max-width: 100%;
}

.user-list-container h2 {
  font-size: 1.75rem;
  color: #333;
  margin-bottom: 1rem;
  font-weight: 600;
}

.user-list-container .btn-primary {
  background-color: #007bff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.3s;
}

.user-list-container .btn-primary:hover {
  background-color: #0056b3;
}

.filter-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.filter-status select {
  padding: 0.3rem 0.6rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: white;
  cursor: pointer;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.table thead {
  background: #007bff;
  color: white;
}

.table thead th {
  padding: 0.8rem;
  text-align: left;
  font-size: 0.9rem;
  font-weight: 600;
}

.table tbody tr {
  border-bottom: 1px solid #eee;
  transition: background 0.2s;
}

.table tbody tr:hover {
  background-color: #f1f8ff;
}

.table tbody td {
  padding: 0.7rem;
  vertical-align: middle;
  font-size: 0.9rem;
  color: #444;
}

.table img {
  border-radius: 50%;
  border: 1px solid #ddd;
}

.table .btn {
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
}

.btn-warning {
  background-color: #ffc107;
  border: none;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  border: none;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* Responsive table */
@media (max-width: 768px) {
  .user-list-container {
    padding: 1rem;
  }

  .table thead {
    display: none;
  }

  .table tbody tr {
    display: block;
    margin-bottom: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 0.5rem;
  }

  .table tbody tr td {
    display: flex;
    justify-content: space-between;
    padding: 0.4rem 0;
    border-bottom: 1px dashed #eee;
  }

  .table tbody tr td:last-child {
    border-bottom: none;
  }

  .table tbody tr td::before {
    content: attr(data-label);
    font-weight: bold;
    color: #555;
  }
}
