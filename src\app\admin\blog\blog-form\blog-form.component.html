<div class="blog-form-container">
    <span *ngIf="isLoading" class="loader"></span>

    <h2>{{ isEdit ? 'Sửa' : 'Thêm mới' }} Blog</h2>

    <form #f="ngForm" (ngSubmit)="save()" novalidate>
        <div class="form-group">
            <label>Tiêu đề:</label>
            <input class="form-control" [(ngModel)]="model.title" name="title" required />
        </div>

        <div class="form-group">
      <label>Nội dung:</label>
      <textarea class="form-control" [(ngModel)]="model.content" name="content" required rows="6"></textarea>
    </div>

    <div class="form-group">
      <div>
        <label>Hình ảnh:</label>
        <input type="file" (change)="onFileSelected($event)" name="file" required />
      </div>
      <div *ngIf="model.image" class="image-preview mt-2">
        <img [src]="getImage(model.image)" alt="image" width="100" height="60">
      </div>
    </div>
    <!-- <PERSON><PERSON><PERSON> c<PERSON>nh thì hiển thị div nếu không có ảnh thì không hiển thị -->

    <div class="form-actions mt-3">
      <button type="submit" class="btn btn-primary" [disabled]="f.invalid">Lưu</button>
      <button type="button" class="btn btn-secondary ml-2" routerLink="/admin/blogs">Hủy</button>
    </div>
    </form>

</div>