<div class="outer">
    <div class="container">
        <div class="row">
            <div class="offset-md-2 col-lg-5 col-md-7 offset-lg-4 offset-md-3">
                <div class="panel border bg-white">
                    <div class="panel-heading">
                        <h3 class="pt-3 font-weight-bold">Login</h3>
                    </div>
                    <div class="panel-body p-3">
                        <!-- Form đăng nhập chỉ hiện khi step === 'login' -->
                        <form *ngIf="step === 'login'" (ngSubmit)="login()" #f="ngForm">
                            <div class="form-group pt-3">
                                <div class="input-field">
                                    <span class="fa fa-user p-2"></span>
                                    <input type="text" placeholder="Username" [(ngModel)]="model.username"
                                        name="username" required class="form-control">
                                </div>
                            </div>
                            <div class="form-group pt-3">
                                <div class="input-field">
                                    <span class="fa fa-lock p-2"></span>
                                    <input [type]="type" placeholder="Password" [(ngModel)]="model.password"
                                        name="password" required class="form-control">
                                    <span (click)="showPassword()" class="fa" [ngClass]="eyeIcon"></span>
                                </div>
                            </div>
                            <div class="form-inline fr mt-4">
                                <a href="forget" class="font-weight-bold">Forget Password?</a>
                            </div>
                            <button type="submit" class="btn btn-primary btn-block mt-3 w-100"
                                [disabled]="f.invalid">Login</button>
                            <div *ngIf="error" style="color:red; margin-top:10px;">{{error}}</div>
                        </form>

                        <!-- Form OTP chỉ hiện khi step === 'otp' -->
                        <form *ngIf="step === 'otp'" (ngSubmit)="verifyOtp()" #otpForm="ngForm">
                            <div class="form-group pt-3">
                                <label>Nhập mã OTP đã gửi:</label>
                                <input type="text" [(ngModel)]="otp" name="otp" required class="form-control" placeholder="OTP">
                            </div>
                            <button type="submit" class="btn btn-success btn-block mt-3 w-100" [disabled]="otpForm.invalid">Xác nhận OTP</button>
                            <div *ngIf="error" style="color:red; margin-top:10px;">{{error}}</div>
                        </form>
                        
                        <div class="text-center pt-4 text-muted">Don't have an account?
                            <a routerLink="/register">Register</a>
                        </div>
                    </div>
                    <div class="mx-3 my-2 py-2 bordert">
                        <div class="text-center py-3">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>