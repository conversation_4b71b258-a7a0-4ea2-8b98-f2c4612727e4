form {
  max-width: 480px;
  margin: 40px auto;
  padding: 30px 35px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
}

form div {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

label {
  font-weight: 700;
  font-size: 1.05rem;
  margin-bottom: 8px;
  color: #222;
  letter-spacing: 0.02em;
}

input[type="text"],
input[type="number"],
textarea,
select {
  padding: 12px 14px;
  font-size: 1rem;
  border: 1.8px solid #ddd;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #444;
  box-sizing: border-box;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #3b82f6; /* m<PERSON>u xanh t<PERSON>ơi */
  background-color: #fff;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
}

textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

button[type="submit"] {
  width: 100%;
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  border: none;
  border-radius: 10px;
  padding: 14px 0;
  font-size: 1.15rem;
  font-weight: 700;
  color: white;
  cursor: pointer;
  transition: background 0.4s ease;
  box-shadow: 0 6px 12px rgba(37, 99, 235, 0.5);
}

button[type="submit"]:hover:not(:disabled) {
  background: linear-gradient(90deg, #2563eb, #1e40af);
  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.6);
}

button[type="submit"]:disabled {
  background-color: #cbd5e1;
  cursor: not-allowed;
  box-shadow: none;
  color: #94a3b8;
}

.loader {
    width: 48px;
    height: 48px;
    border: 5px solid #FFF;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
    }

    @keyframes rotation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
    } 
