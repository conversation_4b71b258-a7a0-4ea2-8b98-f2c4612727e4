.user-form-container {
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 12px;
  max-width: 500px;
  margin: 2rem auto;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
  position: relative;
  font-family: Arial, sans-serif;
}

.user-form-container h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 600;
}

.loader {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  border: 4px solid #ccc;
  border-top-color: #007bff;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  font-weight: 500;
  color: #444;
  display: block;
  margin-bottom: 0.4rem;
}

.form-control,
input[type="file"] {
  width: 100%;
  padding: 0.45rem 0.7rem;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 0.95rem;
  box-sizing: border-box;
}

.form-control:focus,
input[type="file"]:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
  outline: none;
}

.form-group input[type="checkbox"] {
  transform: scale(1.2);
  margin-right: 0.5rem;
  cursor: pointer;
}

.image-preview {
  margin-top: 0.5rem;
}

.image-preview img {
  border: 2px solid #ddd;
  padding: 2px;
  background: white;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.form-actions .btn {
  padding: 0.5rem 1.25rem;
  border-radius: 6px;
  cursor: pointer;
  border: none;
  font-size: 0.9rem;
  transition: background 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

/* Responsive */
@media (max-width: 576px) {
  .user-form-container {
    padding: 1rem;
  }
  .form-actions {
    flex-direction: column;
    width: 100%;
  }
  .form-actions .btn {
    width: 100%;
    text-align: center;
  }
}
