<header class="user-header">
  <div class="logo">📚 Ebook Store</div>
  <nav class="nav-links">
    <a routerLink="home">Trang chủ</a>
    <a routerLink="ebooks">Ebooks</a>
    
  </nav>
  <div class="user-info" *ngIf="user && user.username">
    <img [src]="getAvatar()" alt="avatar" width="50" height="50" style="object-fit:cover;border-radius:50%">
    <span>Xin chào, <b>{{user.username}}</b></span>
    <a (click)="logout()" class="btn btn-danger mt-3 w-100">Đăng xuất</a>
  </div>
</header>
