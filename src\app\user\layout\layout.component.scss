/* styles.css hoặc tương ứng */

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f9f9f9;
  margin: 0;
  padding: 0;
}

app-user-header {
  display: block;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-content {
  max-width: 1200px;
  margin: 40px auto;
  padding: 0 20px;
  background-color: #fff;
  min-height: 400px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

app-footer {
  display: block;
  margin-top: 60px;
  padding: 5px 5px;
  color: #ffffff;
  text-align: center;
  font-size: 40px;
  border-top: 5px solid #1976d2; /* <PERSON>i<PERSON>n xanh sáng hơn */
}

